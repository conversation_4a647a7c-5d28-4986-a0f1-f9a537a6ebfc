"""
测试排序功能修改
验证取消默认排序，用户选择排序方法后记住偏好
"""
import os
import tempfile
import shutil
import tkinter as tk
import time


def create_test_files_for_sorting():
    """创建用于测试排序的文件"""
    base_dir = tempfile.mkdtemp(prefix="sort_test_")
    print(f"创建测试目录: {base_dir}")
    
    # 创建不同大小的文件组
    test_groups = [
        # 小文件组 (50字节)
        ("small1.txt", "A" * 50),
        ("small2.txt", "B" * 50),
        
        # 中等文件组 (200字节)
        ("medium1.txt", "C" * 200),
        ("medium2.txt", "D" * 200),
        ("medium3.txt", "E" * 200),
        
        # 大文件组 (500字节)
        ("large1.txt", "F" * 500),
        ("large2.txt", "G" * 500),
    ]
    
    for filename, content in test_groups:
        file_path = os.path.join(base_dir, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"创建文件: {filename} ({len(content)} 字节)")
    
    return base_dir


def test_sorting_functionality():
    """测试排序功能修改"""
    print("=" * 60)
    print("排序功能修改测试")
    print("=" * 60)
    
    # 创建测试文件
    test_dir = create_test_files_for_sorting()
    
    try:
        # 导入主程序
        import sys
        sys.path.append('.')
        
        # 创建主窗口
        root = tk.Tk()
        root.title("排序功能修改测试")
        root.geometry("1400x800")
        
        # 导入并创建应用实例
        from 重复文件查找器 import FileSearchApp
        app = FileSearchApp(root)
        
        print("1. 检查排序相关组件...")
        
        # 检查新的排序按钮
        sort_buttons = [
            ('size_sort_desc_btn', '大小降序按钮'),
            ('size_sort_asc_btn', '大小升序按钮'),
            ('duration_sort_desc_btn', '时长降序按钮'),
            ('duration_sort_asc_btn', '时长升序按钮')
        ]
        
        all_buttons_exist = True
        for attr_name, display_name in sort_buttons:
            if hasattr(app, attr_name):
                print(f"✓ {display_name}存在")
            else:
                print(f"❌ {display_name}不存在")
                all_buttons_exist = False
        
        if not all_buttons_exist:
            print("❌ 排序按钮缺失")
            return False
        
        print("2. 检查用户排序偏好变量...")
        
        # 检查排序偏好变量
        if hasattr(app, 'user_sort_preference'):
            initial_preference = app.user_sort_preference
            print(f"✓ 用户排序偏好变量存在，初始值: {initial_preference}")
            
            if initial_preference is None:
                print("✓ 初始排序偏好为None（未选择）")
            else:
                print(f"⚠ 初始排序偏好不为None: {initial_preference}")
        else:
            print("❌ 用户排序偏好变量不存在")
            return False
        
        print("3. 设置测试目录并进行搜索...")
        
        # 设置测试目录
        app.current_directory = test_dir
        app.path_var.set(f"当前文件夹: {test_dir}")
        app.search_by_size_btn.configure(state='normal')
        app.search_by_duration_btn.configure(state='normal')
        
        # 等待界面更新
        root.update()
        time.sleep(0.2)
        
        # 模拟搜索结果
        mock_results = {}
        
        # 收集所有文件并按大小分组
        for filename in os.listdir(test_dir):
            file_path = os.path.join(test_dir, filename)
            if os.path.isfile(file_path):
                file_size = os.path.getsize(file_path)
                if file_size not in mock_results:
                    mock_results[file_size] = []
                mock_results[file_size].append(file_path)
        
        # 只保留有多个文件的组
        duplicate_groups = {}
        for size, files in mock_results.items():
            if len(files) > 1:
                duplicate_groups[size] = files
        
        print(f"模拟找到 {len(duplicate_groups)} 个重复文件组")
        
        # 设置搜索结果
        app.current_results = duplicate_groups
        app.current_search_type = "size"
        
        print("4. 测试无排序状态的显示...")
        
        # 模拟显示结果（不应该自动排序）
        app.display_results(duplicate_groups)
        root.update()
        
        # 检查统计信息是否正确显示
        stats_text = app.result_stats_var.get()
        expected_count = len(duplicate_groups)
        expected_text = f"找到 {expected_count} 个大小相同的文件组"
        
        if stats_text == expected_text:
            print("✓ 统计信息显示正确")
        else:
            print(f"❌ 统计信息显示错误: {stats_text} != {expected_text}")
            return False
        
        print("5. 测试用户选择降序排序...")
        
        # 模拟用户点击降序排序按钮
        app.sort_by_size_clicked('desc')
        root.update()
        time.sleep(0.2)
        
        # 检查用户偏好是否被记录
        if app.user_sort_preference == 'desc':
            print("✓ 用户排序偏好已记录为降序")
        else:
            print(f"❌ 用户排序偏好记录错误: {app.user_sort_preference} != 'desc'")
            return False
        
        print("6. 测试用户选择升序排序...")
        
        # 模拟用户点击升序排序按钮
        app.sort_by_size_clicked('asc')
        root.update()
        time.sleep(0.2)
        
        # 检查用户偏好是否被更新
        if app.user_sort_preference == 'asc':
            print("✓ 用户排序偏好已更新为升序")
        else:
            print(f"❌ 用户排序偏好更新错误: {app.user_sort_preference} != 'asc'")
            return False
        
        print("7. 测试配置保存和加载...")
        
        # 保存配置
        app.save_config()
        
        # 创建新的应用实例来测试配置加载
        root2 = tk.Tk()
        root2.title("配置加载测试")
        root2.geometry("800x600")
        
        app2 = FileSearchApp(root2)
        
        # 检查配置是否正确加载
        if app2.user_sort_preference == 'asc':
            print("✓ 用户排序偏好配置加载正确")
        else:
            print(f"❌ 用户排序偏好配置加载错误: {app2.user_sort_preference} != 'asc'")
            return False
        
        print("8. 测试排序状态显示...")
        
        # 设置测试数据
        app2.current_results = duplicate_groups
        app2.current_search_type = "size"
        
        # 模拟显示结果（应该使用保存的排序偏好）
        app2.display_results(duplicate_groups)
        root2.update()
        
        # 检查排序状态标签
        sort_status = app2.sort_status_label.cget('text')
        if '从小到大' in sort_status:
            print("✓ 排序状态显示正确（升序）")
        else:
            print(f"❌ 排序状态显示错误: {sort_status}")
            return False
        
        print("9. 所有测试通过！")
        
        # 关闭窗口
        root.after(1000, root.quit)
        root2.after(1000, root2.quit)
        root.mainloop()
        root2.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理测试目录
        try:
            shutil.rmtree(test_dir)
            print(f"清理测试目录: {test_dir}")
        except Exception as e:
            print(f"清理测试目录失败: {e}")
        
        # 清理配置文件
        try:
            if 'app' in locals():
                config_file = app.config_file
                if os.path.exists(config_file):
                    os.remove(config_file)
                    print(f"清理配置文件: {config_file}")
        except Exception as e:
            print(f"清理配置文件失败: {e}")


def main():
    """主函数"""
    try:
        success = test_sorting_functionality()
        
        if success:
            print("\n🎉 排序功能修改测试通过！")
            print("✓ 取消了默认排序")
            print("✓ 用户可以选择升序或降序")
            print("✓ 排序偏好被正确记录和保存")
            print("✓ 配置加载功能正常")
            print("✓ 排序状态显示正确")
            print("\n功能改进总结:")
            print("• 初始显示结果时不进行自动排序")
            print("• 提供4个排序按钮：大小升序/降序、时长升序/降序")
            print("• 记住用户的排序选择，下次使用时自动应用")
            print("• 排序偏好持久化保存到配置文件")
            print("• 排序状态标签显示当前的排序方式")
        else:
            print("\n❌ 测试失败！需要进一步检查实现。")
            
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
